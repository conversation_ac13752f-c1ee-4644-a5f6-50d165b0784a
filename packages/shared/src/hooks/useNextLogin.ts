import {
  getIframe,
  getIsWaveEnv,
  getZtTicketInWave,
  refreshZtTokenInWave,
  waveLogin,
} from '@shared/utils/utils';
import { useLayoutEffect, useRef, useState } from 'react';
import type { Env, Response } from '@shared/types';
import {
  authApiPrefix,
  moatClientId,
  preAuthApiPrefix,
  WAVE_SILENCE_WORK_CODE,
} from '@shared/utils/map';
import { getOTPToken } from '@hoyowave/jsapi';
import {
  authnV3SessionCheck,
  shimTicketLogin,
  syncSessionToMoat,
} from '@shared/routes';
import request from '@shared/utils/request';
import { dispatch } from '@shared/utils/store';
import usePageConfig from '@shared/hooks/usePageConfig';

const useNextLogin = (env: Env, waveOtp: string, clientId: string) => {
  const isWaveEnv = getIsWaveEnv();
  const isIframe = getIframe();
  const [isLogin, setIsLogin] = useState<boolean | undefined>(undefined);
  const [workCode, setWorkCode] = useState<number>();
  const [mfa, setMfa] = useState<boolean>(false);
  const [ticket, setTicket] = useState<string>('');
  const _waveOtp = useRef<string>('');
  const { data } = usePageConfig();
  useLayoutEffect(() => {
    if (clientId) {
      dispatch({ clientId: clientId }, false, 'clientId');
    }
    changeIsLogin();
  }, [waveOtp, env, clientId]);

  const changeIsLogin = async () => {
    const isLogin = (await SyncSessionToMoat()) || (await loginByWave());

    setIsLogin(isLogin);
  };

  const SyncSessionToMoat = async () => {
    // const shim = getShim(env);
    if (isIframe) {
      return false;
    }
    const preAuthApi = preAuthApiPrefix[env];
    const data = await request(`${preAuthApi}/${syncSessionToMoat}`, {
      clientId: moatClientId[env],
    });
    if (data?.code === 0) {
      const result = await request<Response>(
        `${authApiPrefix[env]}/${shimTicketLogin}`,
        { ticket: data?.data?.ticket },
      );
      // return result?.code === 0;
      return result?.code === 0;
    }
    return false;
  };

  const sessionCheck = async () => {
    const data = await request(
      `${authApiPrefix[env]}/${authnV3SessionCheck}`,
      {},
    );
    if (data?.code === 0) {
      return true;
    }
    if (data?.code === -5) {
      setMfa(true);
    }
    return false;
  };

  const loginByWave = async () => {
    let zt_ticket: string | undefined;
    if (waveOtp) {
      _waveOtp.current = waveOtp;
    }
    if (!isIframe && isWaveEnv) {
      zt_ticket = await getZtTicketInWave();
      if (!zt_ticket) {
        await refreshZtTokenInWave();
      }
      const { otp } = await getOTPToken();
      _waveOtp.current = otp;
    }
    if (!_waveOtp.current) {
      if (isWaveEnv) {
        return false;
      } else {
        if (isIframe) {
          return false;
        } else {
          return sessionCheck();
        }
      }
    }
    const data = await waveLogin(_waveOtp.current, env, zt_ticket);
    if (data?.code == 0) {
      setTicket(data?.data?.ticket);
      return true;
    }

    if (data?.code === WAVE_SILENCE_WORK_CODE) {
      setWorkCode(data?.code);
      return false;
    }
    return sessionCheck();
  };
  return {
    isLogin,
    workCode,
    mfa,
    ticket,
  };
};

export default useNextLogin;
